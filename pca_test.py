import os
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from src.pca import PCA

# Set data path
DATA_PATH = './data'

def read_image():
    """
    Read the image subject_05_17.png
    """
    file_path = './data/subject_05_17.png'
    img = Image.open(file_path).convert("L")
    img_array = np.array(img)
    img_vector = img_array.flatten()
    img_vector = img_vector/255.0
    return np.array(img_vector, dtype='float'), img_array.shape

def load_data(split: str) -> tuple[np.ndarray, np.ndarray]:
    """
    Load the data from the specified split
    """
    data_path = DATA_PATH+'/'+split
    files = os.listdir(data_path)
    image_vectors = []
    label_vectors = []

    for f in files:
        # Read the image using PIL
        img = Image.open(data_path + '/'+f).convert("L")
        f_name, f_type = os.path.splitext(f)
        label = int(f_name[-2:])-1
        label_vectors.append(label)

        # Convert the image to a numpy array
        img_array = np.array(img)

        # Reshape the image into a vector
        img_vector = img_array.flatten()
        img_vector = img_vector/255.0
        image_vectors.append(img_vector)

    return np.array(image_vectors), np.array(label_vectors)

def reconstruction_loss(img_vec: np.ndarray, img_vec_reconstructed: np.ndarray) -> float:
    """
    Calculate the reconstruction loss
    """
    return ((img_vec - img_vec_reconstructed)**2).mean()

def main():
    # Load training data
    print("Loading training data...")
    X_train, y_train = load_data("train")
    
    # Create PCA model with n_components=40
    n_components = 40
    pca = PCA(n_components=n_components)
    
    # Fit PCA model
    print(f"Fitting PCA model with {n_components} components...")
    pca.fit(X_train)
    
    # Load the test image
    print("Loading test image...")
    img_vec, img_shape = read_image()
    
    # Reconstruct the image
    print("Reconstructing image...")
    img_reconstructed = pca.reconstruct(img_vec)
    
    # Calculate reconstruction loss
    loss = reconstruction_loss(img_vec, img_reconstructed)
    print(f"Reconstruction loss: {loss:.6f}")
    
    # Reshape vectors back to images
    img_original = img_vec.reshape(img_shape)
    img_reconstructed = img_reconstructed.reshape(img_shape)
    
    # Plot the original and reconstructed images
    plt.figure(figsize=(10, 5))
    
    plt.subplot(1, 2, 1)
    plt.imshow(img_original, cmap='gray')
    plt.title('Original Image')
    plt.axis('off')
    
    plt.subplot(1, 2, 2)
    plt.imshow(img_reconstructed, cmap='gray')
    plt.title(f'Reconstructed Image\n(n_components={n_components})')
    plt.axis('off')
    
    plt.suptitle(f'PCA Reconstruction (Loss: {loss:.6f})')
    plt.tight_layout()
    
    # Save the figure
    plt.savefig('pca_reconstruction.png')
    print("Figure saved as 'pca_reconstruction.png'")
    
    # Show the figure
    plt.show()

if __name__ == "__main__":
    main()
