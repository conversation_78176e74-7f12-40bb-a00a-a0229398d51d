import torch
import numpy as np
from tqdm.auto import tqdm
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt

"""
Implementation of Autoencoder
"""
class Autoencoder(nn.Module):
    def __init__(self, input_dim: int, encoding_dim: int) -> None:
        """
        Modify the model architecture here for comparison
        """
        super(Autoencoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, encoding_dim),
            nn.Linear(encoding_dim, encoding_dim//2),
            nn.ReLU()
        )
        self.decoder = nn.Sequential(
            nn.Linear(encoding_dim//2, encoding_dim),
            nn.Linear(encoding_dim, input_dim),
        )
    
    def forward(self, x):
        encoded = self.encoder(x)
        decoded = self.decoder(encoded)
        return decoded
    
    def fit(self, X, epochs=10, batch_size=32):
        if isinstance(X, np.ndarray):
            X = torch.tensor(X, dtype=torch.float32)

        optimizer = optim.Adam(self.parameters(), lr=0.001)
        ls_func = nn.MSELoss()

        ls_history = []
        for epoch in tqdm(range(epochs), desc="Training Autoencoder"):
            epoch_loss = 0.0
            permutation = torch.randperm(X.size(0))
            n_batches = X.size(0) // batch_size
            for i in range(n_batches):
                idx = permutation[i * batch_size:(i + 1) * batch_size]
                batch_X = X[idx]

                optimizer.zero_grad()
                outputs = self.forward(batch_X)

                loss = ls_func(outputs, batch_X)

                loss.backward()
                optimizer.step()

                epoch_loss += loss.item()

            ls_history.append(epoch_loss / n_batches)

        plt.figure(figsize=(10, 5))
        plt.plot(ls_history)
        plt.title('Autoencoder Training Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Average Reconstruction Error')
        plt.grid(True)
        plt.savefig('autoencoder_training_loss.png')

        return ls_history
    
    def transform(self, X):
        if isinstance(X, np.ndarray):
            X = torch.tensor(X, dtype=torch.float32)

        self.eval()

        with torch.no_grad():
            encoded = self.encoder(X)

        return encoded.numpy()
    
    def reconstruct(self, X):
        if isinstance(X, np.ndarray):
            X = torch.tensor(X, dtype=torch.float32)

        self.eval()

        with torch.no_grad():
            outputs = self.forward(X)

        return outputs.numpy()


"""
Implementation of DenoisingAutoencoder
"""
class DenoisingAutoencoder(Autoencoder):
    def __init__(self, input_dim, encoding_dim, noise_factor=0.2):
        super(DenoisingAutoencoder, self).__init__(input_dim,encoding_dim)
        self.noise_factor = noise_factor
    
    def add_noise(self, x):
        noise = torch.randn_like(x) * self.noise_factor
        return x + noise
    
    def fit(self, X, epochs=10, batch_size=32):
        if isinstance(X, np.ndarray):
            X = torch.tensor(X, dtype=torch.float32)

        optimizer = optim.Adam(self.parameters(), lr=0.001)
        ls_func = nn.MSELoss()

        ls_history = []
        for epoch in tqdm(range(epochs), desc="Training denoising autoencoder"):
            epoch_loss = 0.0
            permutation = torch.randperm(X.size(0))
            n_batches = X.size(0) // batch_size
            for i in range(n_batches):
                idx = permutation[i * batch_size:(i + 1) * batch_size]
                batch_X = X[idx]
                noisy_batch_X = self.add_noise(batch_X)

                optimizer.zero_grad()
                outputs = self.forward(noisy_batch_X)

                loss = ls_func(outputs, batch_X)

                loss.backward()
                optimizer.step()

                epoch_loss += loss.item()

            ls_history.append(epoch_loss / n_batches)

        plt.figure(figsize=(10, 5))
        plt.plot(ls_history)
        plt.title('Denoising autoencoder Training Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Average Reconstruction Error')
        plt.grid(True)
        plt.savefig('denoising_autoencoder_training_loss.png')

        return ls_history
