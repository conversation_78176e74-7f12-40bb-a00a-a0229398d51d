import torch
import numpy as np
from tqdm.auto import tqdm
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt

"""
Implementation of Autoencoder
"""
class Autoencoder(nn.Module):
    def __init__(self, input_dim: int, encoding_dim: int) -> None:
        """
        Modify the model architecture here for comparison
        """
        super(Autoencoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, encoding_dim),
            nn.Linear(encoding_dim, encoding_dim//2),
            nn.ReLU()
        )
        self.decoder = nn.Sequential(
            nn.Linear(encoding_dim//2, encoding_dim),
            nn.Linear(encoding_dim, input_dim),
        )

    def forward(self, x):
        """
        Forward pass through the autoencoder
        Args:
            x: Input tensor
        Returns:
            Reconstructed tensor
        """
        encoded = self.encoder(x)
        decoded = self.decoder(encoded)
        return decoded

    def fit(self, X, epochs=10, batch_size=32):
        """
        Train the autoencoder to minimize reconstruction error
        Args:
            X: Training data (numpy array)
            epochs: Number of training epochs
            batch_size: Batch size for training
        Returns:
            List of average reconstruction errors per epoch
        """
        # Convert numpy array to torch tensor if needed
        if isinstance(X, np.ndarray):
            X = torch.tensor(X, dtype=torch.float32)

        # Define optimizer and loss function
        optimizer = optim.Adam(self.parameters(), lr=0.001)
        criterion = nn.MSELoss()

        # Track loss history
        loss_history = []

        # Training loop
        for epoch in tqdm(range(epochs), desc="Training Autoencoder"):
            epoch_loss = 0.0
            # Create batches
            permutation = torch.randperm(X.size(0))
            num_batches = X.size(0) // batch_size

            for i in range(num_batches):
                indices = permutation[i * batch_size:(i + 1) * batch_size]
                batch_x = X[indices]

                # Forward pass
                optimizer.zero_grad()
                outputs = self.forward(batch_x)

                # Compute loss
                loss = criterion(outputs, batch_x)

                # Backward pass and optimize
                loss.backward()
                optimizer.step()

                epoch_loss += loss.item()

            # Record average loss for this epoch
            avg_loss = epoch_loss / num_batches
            loss_history.append(avg_loss)

            if (epoch + 1) % 50 == 0:
                print(f'Epoch [{epoch+1}/{epochs}], Loss: {avg_loss:.6f}')

        # Plot training loss
        plt.figure(figsize=(10, 5))
        plt.plot(loss_history)
        plt.title('Autoencoder Training Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Average Reconstruction Error')
        plt.grid(True)
        plt.savefig('autoencoder_training_loss.png')

        return loss_history

    def transform(self, X):
        """
        Transform data to lower dimension using the encoder
        Args:
            X: Input data (numpy array or torch tensor)
        Returns:
            Encoded data (numpy array)
        """
        # Convert to torch tensor if needed
        if isinstance(X, np.ndarray):
            X = torch.tensor(X, dtype=torch.float32)

        # Set model to evaluation mode
        self.eval()

        # Pass through encoder
        with torch.no_grad():
            encoded = self.encoder(X)

        # Return as numpy array
        return encoded.numpy()

    def reconstruct(self, X):
        """
        Reconstruct input data by passing through encoder and decoder
        Args:
            X: Input data (numpy array or torch tensor)
        Returns:
            Reconstructed data (numpy array)
        """
        # Convert to torch tensor if needed
        if isinstance(X, np.ndarray):
            X = torch.tensor(X, dtype=torch.float32)

        # Set model to evaluation mode
        self.eval()

        # Pass through autoencoder
        with torch.no_grad():
            reconstructed = self.forward(X)

        # Return as numpy array
        return reconstructed.numpy()


"""
Implementation of DenoisingAutoencoder
"""
class DenoisingAutoencoder(Autoencoder):
    def __init__(self, input_dim, encoding_dim, noise_factor=0.2):
        super(DenoisingAutoencoder, self).__init__(input_dim,encoding_dim)
        self.noise_factor = noise_factor

    def add_noise(self, x):
        """
        Add Gaussian noise to the input data
        Args:
            x: Input tensor
        Returns:
            Input tensor with added noise
        """
        # Generate noise with the same shape as x
        noise = torch.randn_like(x) * self.noise_factor
        # Add noise to the input
        noisy_x = x + noise
        return noisy_x

    def fit(self, X, epochs=10, batch_size=32):
        """
        Train the denoising autoencoder to minimize reconstruction error
        Args:
            X: Training data (numpy array)
            epochs: Number of training epochs
            batch_size: Batch size for training
        Returns:
            List of average reconstruction errors per epoch
        """
        # Convert numpy array to torch tensor if needed
        if isinstance(X, np.ndarray):
            X = torch.tensor(X, dtype=torch.float32)

        # Define optimizer and loss function
        optimizer = optim.Adam(self.parameters(), lr=0.001)
        criterion = nn.MSELoss()

        # Track loss history
        loss_history = []

        # Training loop
        for epoch in tqdm(range(epochs), desc="Training DenoisingAutoencoder"):
            epoch_loss = 0.0
            # Create batches
            permutation = torch.randperm(X.size(0))
            num_batches = X.size(0) // batch_size

            for i in range(num_batches):
                indices = permutation[i * batch_size:(i + 1) * batch_size]
                batch_x = X[indices]

                # Add noise to input
                noisy_batch_x = self.add_noise(batch_x)

                # Forward pass
                optimizer.zero_grad()
                outputs = self.forward(noisy_batch_x)

                # Compute loss (between original clean data and reconstruction)
                loss = criterion(outputs, batch_x)

                # Backward pass and optimize
                loss.backward()
                optimizer.step()

                epoch_loss += loss.item()

            # Record average loss for this epoch
            avg_loss = epoch_loss / num_batches
            loss_history.append(avg_loss)

            if (epoch + 1) % 50 == 0:
                print(f'Epoch [{epoch+1}/{epochs}], Loss: {avg_loss:.6f}')

        # Plot training loss
        plt.figure(figsize=(10, 5))
        plt.plot(loss_history)
        plt.title('Denoising Autoencoder Training Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Average Reconstruction Error')
        plt.grid(True)
        plt.savefig('denoising_autoencoder_training_loss.png')

        return loss_history
