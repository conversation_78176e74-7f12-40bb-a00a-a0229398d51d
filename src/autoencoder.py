import torch
from tqdm.auto import tqdm
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt

"""
Implementation of Autoencoder
"""
class Autoencoder(nn.Module):
    def __init__(self, input_dim: int, encoding_dim: int) -> None:
        """
        Modify the model architecture here for comparison
        """
        super(Autoencoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, encoding_dim),
            nn.Linear(encoding_dim, encoding_dim//2),
            nn.ReLU()
        )
        self.decoder = nn.Sequential(
            nn.Linear(encoding_dim//2, encoding_dim),
            nn.Linear(encoding_dim, input_dim),
        )

    def forward(self, x):
        """
        Forward pass through the autoencoder.

        Parameters:
        -----------
        x : torch.Tensor
            Input data

        Returns:
        --------
        x_reconstructed : torch.Tensor
            Reconstructed data
        """
        # Encode the input
        encoded = self.encoder(x)
        # Decode the encoded representation
        decoded = self.decoder(encoded)
        return decoded

    def fit(self, X, epochs=10, batch_size=32):
        """
        Train the autoencoder to minimize reconstruction error.

        Parameters:
        -----------
        X : numpy.ndarray
            Training data of shape (n_samples, n_features)
        epochs : int
            Number of epochs to train
        batch_size : int
            Batch size for training

        Returns:
        --------
        losses : list
            List of training losses per epoch
        """
        # Convert numpy array to torch tensor
        X_tensor = torch.tensor(X, dtype=torch.float32)

        # Define loss function and optimizer
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.parameters(), lr=0.001)

        # Track losses for plotting
        losses = []

        # Set model to training mode
        self.train()

        # Create data loader
        dataset = torch.utils.data.TensorDataset(X_tensor, X_tensor)
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)

        # Training loop
        for epoch in range(epochs):
            running_loss = 0.0

            # Use tqdm for progress bar
            for x_batch, _ in tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}"):
                # Zero the parameter gradients
                optimizer.zero_grad()

                # Forward pass
                outputs = self(x_batch)

                # Compute loss
                loss = criterion(outputs, x_batch)

                # Backward pass and optimize
                loss.backward()
                optimizer.step()

                # Update running loss
                running_loss += loss.item() * x_batch.size(0)

            # Calculate epoch loss
            epoch_loss = running_loss / len(X_tensor)
            losses.append(epoch_loss)

            # Print progress
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{epochs}, Loss: {epoch_loss:.6f}")

        # Plot training curve
        plt.figure(figsize=(10, 5))
        plt.plot(range(1, epochs+1), losses)
        plt.xlabel('Epoch')
        plt.ylabel('MSE Loss')
        plt.title('Autoencoder Training Loss')
        plt.grid(True)
        plt.savefig('autoencoder_training_loss.png')
        print("Training loss curve saved as 'autoencoder_training_loss.png'")

        return losses

    def transform(self, X):
        """
        Transform data using the encoder.

        Parameters:
        -----------
        X : numpy.ndarray
            Data to transform of shape (n_samples, n_features)

        Returns:
        --------
        X_transformed : numpy.ndarray
            Transformed data
        """
        # Convert numpy array to torch tensor
        X_tensor = torch.tensor(X, dtype=torch.float32)

        # Set model to evaluation mode
        self.eval()

        # Transform data
        with torch.no_grad():
            X_transformed = self.encoder(X_tensor)

        # Convert back to numpy array
        return X_transformed.numpy()

    def reconstruct(self, X):
        """
        Reconstruct data using the autoencoder.

        Parameters:
        -----------
        X : torch.Tensor or numpy.ndarray
            Data to reconstruct

        Returns:
        --------
        X_reconstructed : numpy.ndarray
            Reconstructed data
        """
        # Convert to torch tensor if it's a numpy array
        if isinstance(X, np.ndarray):
            X = torch.tensor(X, dtype=torch.float32)

        # Set model to evaluation mode
        self.eval()

        # Reconstruct data
        with torch.no_grad():
            X_reconstructed = self(X)

        # Convert back to numpy array
        return X_reconstructed.numpy()


"""
Implementation of DenoisingAutoencoder
"""
class DenoisingAutoencoder(Autoencoder):
    def __init__(self, input_dim, encoding_dim, noise_factor=0.2):
        super(DenoisingAutoencoder, self).__init__(input_dim,encoding_dim)
        self.noise_factor = noise_factor

    def add_noise(self, x):
        """
        Add random noise to the input data.

        Parameters:
        -----------
        x : torch.Tensor
            Input data

        Returns:
        --------
        x_noisy : torch.Tensor
            Input data with added noise
        """
        # Generate random noise
        noise = torch.randn_like(x) * self.noise_factor

        # Add noise to input
        x_noisy = x + noise

        # Clip values to be between 0 and 1 (since original data is normalized)
        x_noisy = torch.clamp(x_noisy, 0., 1.)

        return x_noisy

    def fit(self, X, epochs=10, batch_size=32):
        """
        Train the denoising autoencoder to minimize reconstruction error.

        Parameters:
        -----------
        X : numpy.ndarray
            Training data of shape (n_samples, n_features)
        epochs : int
            Number of epochs to train
        batch_size : int
            Batch size for training

        Returns:
        --------
        losses : list
            List of training losses per epoch
        """
        # Convert numpy array to torch tensor
        X_tensor = torch.tensor(X, dtype=torch.float32)

        # Define loss function and optimizer
        criterion = nn.MSELoss()
        optimizer = optim.Adam(self.parameters(), lr=0.001)

        # Track losses for plotting
        losses = []

        # Set model to training mode
        self.train()

        # Create data loader
        dataset = torch.utils.data.TensorDataset(X_tensor, X_tensor)
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)

        # Training loop
        for epoch in range(epochs):
            running_loss = 0.0

            # Use tqdm for progress bar
            for x_batch, _ in tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}"):
                # Add noise to input
                x_noisy = self.add_noise(x_batch)

                # Zero the parameter gradients
                optimizer.zero_grad()

                # Forward pass (reconstruct from noisy input)
                outputs = self(x_noisy)

                # Compute loss (between original clean input and reconstruction)
                loss = criterion(outputs, x_batch)

                # Backward pass and optimize
                loss.backward()
                optimizer.step()

                # Update running loss
                running_loss += loss.item() * x_batch.size(0)

            # Calculate epoch loss
            epoch_loss = running_loss / len(X_tensor)
            losses.append(epoch_loss)

            # Print progress
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{epochs}, Loss: {epoch_loss:.6f}")

        # Plot training curve
        plt.figure(figsize=(10, 5))
        plt.plot(range(1, epochs+1), losses)
        plt.xlabel('Epoch')
        plt.ylabel('MSE Loss')
        plt.title('Denoising Autoencoder Training Loss')
        plt.grid(True)
        plt.savefig('denoising_autoencoder_training_loss.png')
        print("Training loss curve saved as 'denoising_autoencoder_training_loss.png'")

        return losses
