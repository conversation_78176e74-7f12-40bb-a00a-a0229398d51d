import numpy as np


"""
Implementation of Principal Component Analysis.
"""
class PCA:
    def __init__(self, n_components: int) -> None:
        self.n_components = n_components
        self.mean = None
        self.components = None

    def fit(self, X: np.ndarray) -> None:
        self.mean = np.mean(X, axis=0)
        X_centered = X - self.mean
        eigenvalues, eigenvectors = np.linalg.eigh(np.dot(X_centered.T, X_centered))
        idx = np.argsort(eigenvalues)[::-1]
        eigenvectors_sorted = eigenvectors[:, idx]
        self.components = eigenvectors_sorted[:, :self.n_components]


    def transform(self, X: np.ndarray) -> np.ndarray:
        X_centered = X - self.mean
        return np.dot(X_centered, self.components)

    def reconstruct(self, X):
        if (len(X.shape) == 1 and X.shape[0] == self.components.shape[0]) or\
           (len(X.shape) == 2 and X.shape[1] == self.components.shape[0]):
            X_transformed = self.transform(X)
        else:
            X_transformed = X
            
        return np.dot(self.components, X_transformed) + self.mean
        
