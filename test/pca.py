import numpy as np


"""
Implementation of Principal Component Analysis.
"""
class PCA:
    def __init__(self, n_components: int) -> None:
        self.n_components = n_components
        self.mean = None
        self.components = None

    def fit(self, X: np.ndarray) -> None:
        """
        Fit the PCA model with the training data.

        Parameters:
        -----------
        X : np.ndarray
            Training data of shape (n_samples, n_features)
        """
        self.mean = np.mean(X, axis=0)

        X_centered = X - self.mean
        n_samples = X.shape[0]

        cov_matrix = np.dot(X_centered.T, X_centered) / (n_samples - 1)

        eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)

        idx = np.argsort(eigenvalues)[::-1]
        eigenvalues = eigenvalues[idx]
        eigenvectors = eigenvectors[:, idx]

        self.components = eigenvectors[:, :self.n_components]

    def transform(self, X: np.ndarray) -> np.ndarray:
        """
        Transform data by projecting it onto the principal components.

        Parameters:
        -----------
        X : np.ndarray
            Data to transform of shape (n_samples, n_features)

        Returns:
        --------
        X_transformed : np.ndarray
            Transformed data of shape (n_samples, n_components)
        """
        X_centered = X - self.mean
        X_transformed = np.dot(X_centered, self.components)

        return X_transformed

    def reconstruct(self, X: np.ndarray) -> np.ndarray:
        """
        Reconstruct data from the PCA representation.

        Parameters:
        -----------
        X : np.ndarray
            Data to reconstruct. If X is already transformed, it should be of shape (n_components,).
            If X is in original space, it should be of shape (n_features,).

        Returns:
        --------
        X_reconstructed : np.ndarray
            Reconstructed data in the original space
        """
        if len(X.shape) == 1:
            if X.shape[0] == self.components.shape[0]:
                X_transformed = np.dot((X - self.mean), self.components)
                X_reconstructed = np.dot(X_transformed, self.components.T) + self.mean
            else:  
                X_reconstructed = np.dot(X, self.components.T) + self.mean
        else:
            if X.shape[1] == self.components.shape[0]:
                X_transformed = self.transform(X)
                X_reconstructed = np.dot(X_transformed, self.components.T) + self.mean
            else:
                X_reconstructed = np.dot(X, self.components.T) + self.mean

        return X_reconstructed

