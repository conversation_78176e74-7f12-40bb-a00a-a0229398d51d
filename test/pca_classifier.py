import os
import numpy as np
from sklearn.linear_model import LogisticRegression
from PIL import Image
from src.pca import PCA

# Set data path
DATA_PATH = './data'

def load_data(split: str) -> tuple[np.ndarray, np.ndarray]:
    """
    Load the data from the specified split
    """
    data_path = DATA_PATH+'/'+split
    files = os.listdir(data_path)
    image_vectors = []
    label_vectors = []

    for f in files:
        # Read the image using PIL
        img = Image.open(data_path + '/'+f).convert("L")
        f_name, f_type = os.path.splitext(f)
        label = int(f_name[-2:])-1
        label_vectors.append(label)

        # Convert the image to a numpy array
        img_array = np.array(img)

        # Reshape the image into a vector
        img_vector = img_array.flatten()
        img_vector = img_vector/255.0
        image_vectors.append(img_vector)

    return np.array(image_vectors), np.array(label_vectors)

def compute_acc(y_pred: np.ndarray, y_val: np.ndarray):
    """
    Compute the accuracy
    """
    return np.sum(y_pred == y_val) / len(y_val)

def main():
    # Load data
    print("Loading data...")
    X_train, y_train = load_data("train")
    X_val, y_val = load_data("val")
    
    # Try different numbers of components
    n_components_list = [5, 10, 20, 40, 80]
    
    for n_components in n_components_list:
        # Create and fit PCA model
        print(f"\nTraining PCA with {n_components} components...")
        pca = PCA(n_components=n_components)
        pca.fit(X_train)
        
        # Transform data
        print("Transforming data...")
        X_train_transformed = pca.transform(X_train)
        X_val_transformed = pca.transform(X_val)
        
        # Train logistic regression
        print("Training logistic regression...")
        clf = LogisticRegression(max_iter=10000, random_state=0)
        clf.fit(X_train_transformed, y_train)
        
        # Make predictions
        y_pred = clf.predict(X_val_transformed)
        
        # Calculate accuracy
        accuracy = compute_acc(y_pred, y_val)
        print(f"Accuracy with {n_components} components: {accuracy:.4f}")

if __name__ == "__main__":
    main()
